* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f0f2f5;
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    height: 100vh;
    background: #ffffff;
}

/* Sidebar */
.sidebar {
    width: 400px;
    background: #ffffff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    min-width: 300px;
}

.sidebar-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.logout-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.search-container {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.search-box {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 12px 20px 12px 45px;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #8e8e93;
    font-size: 16px;
}

.users-list {
    flex: 1;
    overflow-y: auto;
    background: white;
}

.user-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-item:hover {
    background: #f8f9fa;
}

.user-item.active {
    background: #e3f2fd;
    border-right: 3px solid #667eea;
}

.user-item-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: #4caf50;
}

.offline-indicator {
    background: #9e9e9e;
}

.user-item-info {
    flex: 1;
}

.user-item-name {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.user-item-status {
    font-size: 14px;
    color: #7f8c8d;
}

.last-message {
    font-size: 13px;
    color: #95a5a6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.message-time {
    font-size: 12px;
    color: #95a5a6;
    margin-left: auto;
}

.unread-count {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
}

/* Main Chat Area */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.chat-user-info h3 {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 2px;
}

.chat-user-info p {
    font-size: 14px;
    color: #7f8c8d;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23e0e0e0" opacity="0.1"/></svg>');
    background-size: 20px 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.sent .message-bubble {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.received .message-bubble {
    background: white;
    color: #2c3e50;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 4px;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
}

.message.sent .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message.received .message-time {
    color: #95a5a6;
}

.message-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.message-input {
    flex: 1;
    padding: 12px 20px;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    resize: none;
    max-height: 120px;
    min-height: 45px;
    transition: border-color 0.3s ease;
}

.message-input:focus {
    outline: none;
    border-color: #667eea;
}

.send-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.send-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.welcome-screen {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    text-align: center;
}

.welcome-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.welcome-screen h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.welcome-screen p {
    font-size: 16px;
    max-width: 400px;
    line-height: 1.6;
}

.typing-indicator {
    padding: 10px 20px;
    background: rgba(103, 126, 234, 0.1);
    border-radius: 15px;
    margin-bottom: 10px;
    display: none;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typing 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-chat {
        width: 100%;
    }

    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        padding: 10px;
        color: #2c3e50;
    }
}

@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none;
    }
}