// Simple WebSocket test script to verify connection consistency
import WebSocket from 'ws';

const WS_URL = 'ws://localhost:3000';
let testSocket = null;

// Test user data
const testUser = {
    id: 1,
    name: 'Test User'
};

function connectTestSocket() {
    return new Promise((resolve, reject) => {
        console.log('🔌 Connecting to WebSocket server...');
        testSocket = new WebSocket(WS_URL);

        testSocket.onopen = function() {
            console.log('✅ WebSocket connection established');
            resolve();
        };

        testSocket.onmessage = function(event) {
            const message = JSON.parse(event.data);
            console.log('📨 Received message:', message);
        };

        testSocket.onclose = function(event) {
            console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        };

        testSocket.onerror = function(error) {
            console.error('❌ WebSocket error:', error);
            reject(error);
        };

        // Timeout after 5 seconds
        setTimeout(() => {
            if (testSocket.readyState !== WebSocket.OPEN) {
                reject(new Error('Connection timeout'));
            }
        }, 5000);
    });
}

function sendTestMessage(type, data = {}) {
    return new Promise((resolve, reject) => {
        if (!testSocket || testSocket.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket not connected'));
            return;
        }

        const message = {
            type: type,
            userId: testUser.id,
            ...data
        };

        console.log(`📤 Sending ${type} message:`, message);
        testSocket.send(JSON.stringify(message));
        
        // Wait for response
        setTimeout(resolve, 1000);
    });
}

async function testSignInFlow() {
    console.log('\n🧪 Testing Sign-In Flow...');
    
    try {
        // Connect to WebSocket
        await connectTestSocket();
        
        // Test connection
        await sendTestMessage('testConnection');
        
        // Send status message (simulating sign-in)
        await sendTestMessage('status');
        
        console.log('✅ Sign-in flow test completed successfully');
        
    } catch (error) {
        console.error('❌ Sign-in flow test failed:', error.message);
    }
}

async function testLogoutFlow() {
    console.log('\n🧪 Testing Logout Flow...');
    
    try {
        // Send logout message
        await sendTestMessage('logout');
        
        // Wait for connection to close
        await new Promise(resolve => {
            if (testSocket.readyState === WebSocket.CLOSED) {
                resolve();
            } else {
                testSocket.onclose = resolve;
            }
            setTimeout(resolve, 2000); // Timeout after 2 seconds
        });
        
        console.log('✅ Logout flow test completed successfully');
        
    } catch (error) {
        console.error('❌ Logout flow test failed:', error.message);
    }
}

async function testReconnection() {
    console.log('\n🧪 Testing Reconnection...');
    
    try {
        // Connect again after logout
        await connectTestSocket();
        
        // Test connection
        await sendTestMessage('testConnection');
        
        // Send status message again
        await sendTestMessage('status');
        
        console.log('✅ Reconnection test completed successfully');
        
        // Clean up
        if (testSocket) {
            testSocket.close();
        }
        
    } catch (error) {
        console.error('❌ Reconnection test failed:', error.message);
    }
}

async function runTests() {
    console.log('🚀 Starting WebSocket Consistency Tests...\n');
    
    await testSignInFlow();
    await testLogoutFlow();
    await testReconnection();
    
    console.log('\n🏁 All tests completed!');
    process.exit(0);
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted');
    if (testSocket) {
        testSocket.close();
    }
    process.exit(0);
});

// Run tests
runTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
